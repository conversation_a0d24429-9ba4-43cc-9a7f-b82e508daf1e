import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { 
  Card, 
  Tabs, 
  Typography, 
  ScrollArea,
  Avatar,
  Button,
  Textarea,
  Divider
} from '@/shared/components/common';
import { formatTimestamp } from '@/shared/utils/form-date-utils';

import { CommentDto } from '../../types/comment.types';
import { TaskDto } from '../../types/task.types';

interface TaskChatSectionProps {
  taskId: number;
  comments: CommentDto[];
  subtasks: TaskDto[];
  isLoadingComments: boolean;
  isLoadingSubtasks: boolean;
  onAddComment: (content: string) => void;
  onAddSubtask: () => void;
  onRefresh: () => void;
}

/**
 * Chat-like section for comments and subtasks
 */
const TaskChatSection: React.FC<TaskChatSectionProps> = ({
  taskId,
  comments,
  subtasks,
  isLoadingComments,
  isLoadingSubtasks,
  onAddComment,
  onAddSubtask,
  onRefresh,
}) => {
  const { t } = useTranslation(['todolist', 'common']);
  const [activeTab, setActiveTab] = useState<string>('comments');
  const [commentText, setCommentText] = useState('');

  const handleSendComment = () => {
    if (commentText.trim()) {
      onAddComment(commentText.trim());
      setCommentText('');
    }
  };

  const renderComments = () => (
    <div className="flex flex-col h-full">
      {/* Messages area */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full p-4">
          {comments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-8">
              <div className="text-4xl mb-4">💬</div>
              <Typography variant="h6" className="mb-2">
                {t('todolist:task.comments.empty', 'No comments yet')}
              </Typography>
              <Typography variant="body2" className="text-gray-500">
                {t('todolist:task.comments.emptyDescription', 'Be the first to comment!')}
              </Typography>
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment) => (
                <div key={comment.id} className="flex space-x-3">
                  <Avatar
                    src={comment.userAvatar}
                    alt={comment.userName || `User ${comment.userId}`}
                    size="sm"
                    fallback={comment.userName?.[0] || 'U'}
                  />
                  <div className="flex-1">
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <Typography variant="subtitle2" className="font-medium">
                          {comment.userName || `User ${comment.userId}`}
                        </Typography>
                        <Typography variant="caption" className="text-gray-500">
                          {formatTimestamp(comment.createdAt || 0)}
                        </Typography>
                        {comment.updatedAt !== comment.createdAt && (
                          <Typography variant="caption" className="text-gray-500 italic">
                            ({t('todolist:task.comments.edited', 'edited')})
                          </Typography>
                        )}
                      </div>
                      <Typography variant="body2" className="whitespace-pre-wrap">
                        {comment.content}
                      </Typography>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Input area */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex space-x-2">
          <div className="flex-1">
            <Textarea
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              placeholder={t('todolist:task.comments.placeholder', 'Write a comment...')}
              rows={2}
              className="resize-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendComment();
                }
              }}
            />
          </div>
          <Button
            onClick={handleSendComment}
            disabled={!commentText.trim()}
            size="sm"
            variant="primary"
          >
            {t('todolist:task.comments.send', 'Send')}
          </Button>
        </div>
      </div>
    </div>
  );

  const renderSubtasks = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <Typography variant="h6">
            {t('todolist:task.subtasks.title', 'Subtasks')}
          </Typography>
          <Button size="sm" onClick={onAddSubtask}>
            {t('todolist:task.subtasks.add', 'Add Subtask')}
          </Button>
        </div>
      </div>

      {/* Subtasks list */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full p-4">
          {subtasks.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-8">
              <div className="text-4xl mb-4">📝</div>
              <Typography variant="h6" className="mb-2">
                {t('todolist:task.subtasks.empty', 'No subtasks yet')}
              </Typography>
              <Typography variant="body2" className="text-gray-500">
                {t('todolist:task.subtasks.emptyDescription', 'Break down this task into smaller steps')}
              </Typography>
            </div>
          ) : (
            <div className="space-y-3">
              {subtasks.map((subtask) => (
                <div key={subtask.id} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <Typography variant="subtitle2" className="font-medium mb-1">
                        {subtask.title}
                      </Typography>
                      {subtask.description && (
                        <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-2">
                          {subtask.description}
                        </Typography>
                      )}
                      <div className="flex items-center space-x-2">
                        <Typography variant="caption" className="text-gray-500">
                          {formatTimestamp(subtask.createdAt || 0)}
                        </Typography>
                        {subtask.status && (
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            subtask.status === 'completed' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {subtask.status}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );

  return (
    <Card className="h-96">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'comments',
            label: `${t('todolist:task.tabs.comments', 'Comments')} (${comments.length})`,
            children: null,
          },
          {
            key: 'subtasks',
            label: `${t('todolist:task.tabs.subtasks', 'Subtasks')} (${subtasks.length})`,
            children: null,
          },
        ]}
      />
      
      <div className="h-full">
        {activeTab === 'comments' && renderComments()}
        {activeTab === 'subtasks' && renderSubtasks()}
      </div>
    </Card>
  );
};

export default TaskChatSection;
