{"title": "Task Management", "description": "Efficiently manage tasks and projects", "modules": {"dashboard": {"title": "Dashboard", "description": "Overview of tasks and projects"}, "tasks": {"title": "Task List", "description": "Manage tasks to be done", "countLabel": "Tasks"}, "projects": {"title": "Projects", "description": "Manage projects and progress", "countLabel": "Projects"}, "teams": {"title": "Teams", "description": "Manage work teams", "countLabel": "Teams"}, "statistics": {"title": "Statistics", "description": "View reports and task statistics", "countLabel": "Reports"}}, "task": {"table": {"title": "Title", "description": "Description", "status": "Status", "priority": "Priority", "dueDate": "Due Date", "assignee": "Assignee"}, "status": {"todo": "To Do", "inProgress": "In Progress", "done": "Done", "cancelled": "Cancelled"}, "priority": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "form": {"title": "Add New Task", "description": "Enter task information", "edit": "Edit Task"}, "fields": {"title": "Title", "description": "Description", "priority": "Priority", "expectedStars": "Expected Stars", "project": "Project", "status": "Status", "assignee": "Assignee", "createdAt": "Created At", "updatedAt": "Updated At", "completedAt": "Completed At", "awardedStars": "Awarded Stars"}, "placeholders": {"title": "Enter task title", "description": "Enter task description", "priority": "Select priority", "expectedStars": "Enter expected stars (1-5)", "project": "Select project"}, "notifications": {"createSuccess": "Task created successfully", "createError": "Error creating task", "updateSuccess": "Task updated successfully", "updateError": "Error updating task", "deleteSuccess": "Task deleted successfully", "deleteError": "Error deleting task", "statusUpdateSuccess": "Task status updated successfully", "statusUpdateError": "Error updating task status"}, "tabs": {"details": "Details", "subtasks": "Subtasks", "comments": "Comments", "attachments": "Attachments", "keyResults": "Key Results"}, "details": {"title": "Task Details"}, "subtasks": {"title": "Subtasks", "add": "Add Subtask", "empty": "No subtasks yet", "emptyDescription": "Break down this task into smaller steps"}, "comments": {"title": "Comments", "empty": "No comments yet", "emptyDescription": "Be the first to comment!", "edited": "edited", "placeholder": "Write a comment...", "send": "Send", "actions": {"post": "Post Comment"}, "placeholders": {"content": "Write a comment..."}, "notifications": {"createSuccess": "Comment added successfully", "createError": "Error adding comment", "updateSuccess": "Comment updated successfully", "updateError": "Error updating comment", "deleteSuccess": "Comment deleted successfully", "deleteError": "Error deleting comment"}}, "attachments": {"title": "Attachments", "empty": "No attachments yet. Upload files to share with the team.", "uploading": "Uploading Files", "uploadedBy": "Uploaded by", "actions": {"upload": "Upload File", "download": "Download"}, "notifications": {"uploadSuccess": "File uploaded successfully", "uploadError": "Error uploading file", "deleteSuccess": "Attachment deleted successfully", "deleteError": "Error deleting attachment"}}, "viewMode": {"table": "Table", "list": "List"}, "noDescription": "No description provided", "noAssignee": "No assignee", "notFound": "Task not found", "userIdLabel": "User ID", "markComplete": "Mark as complete", "markIncomplete": "Mark as incomplete", "selectAssignee": "Select Assignee", "searchAssignee": "Search for user..."}, "project": {"title": "Project List", "search": "Search projects...", "fields": {"title": "Project Name", "description": "Description", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "ownerId": "Owner ID", "createdBy": "Created By"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"create": "Create New Project", "edit": "Edit Project", "delete": "Delete Project"}, "placeholders": {"title": "Enter project name", "description": "Enter project description"}, "notifications": {"createSuccess": "Project created successfully", "createError": "Error creating project", "updateSuccess": "Project updated successfully", "updateError": "Error updating project", "deleteSuccess": "Project deleted successfully", "deleteError": "Error deleting project"}, "tabs": {"details": "Details", "members": "Members", "tasks": "Tasks"}, "form": {"edit": "Edit Project"}, "members": {"title": "Project Members", "add": "Add Member", "userId": "User ID", "role": "Role", "joinedAt": "Joined At", "empty": "No members found", "placeholders": {"userId": "Enter user ID", "role": "Select role"}, "notifications": {"addSuccess": "Member added successfully", "addError": "Error adding member", "updateSuccess": "Member updated successfully", "updateError": "Error updating member", "deleteSuccess": "Member deleted successfully", "deleteError": "Error deleting member"}}, "roles": {"admin": "Admin", "member": "Member", "viewer": "Viewer"}, "tasks": {"title": "Project Tasks", "add": "Add Task", "empty": "No tasks found", "viewMode": {"table": "Table", "list": "List"}}, "noDescription": "No description provided", "notFound": "Project not found"}, "okr": {"keyResults": {"title": "Linked Key Results", "add": "<PERSON> Result", "empty": "No key results linked to this task yet.", "unlink": "Unlink Key Result"}, "keyResult": {"status": {"notStarted": "Not Started", "inProgress": "In Progress", "completed": "Completed", "atRisk": "At Risk"}, "progress": "Progress", "startValue": "Start Value", "currentValue": "Current Value", "targetValue": "Target Value"}, "form": {"title": "<PERSON> Result", "selectionMode": "Selection Mode", "browseHierarchy": "Browse OKR Hierarchy", "myKeyResults": "My Key Results", "cycle": "OKR Cycle", "selectCycle": "Select OKR Cycle", "objective": "Objective", "selectObjective": "Select Objective", "keyResult": "Key Result", "selectKeyResult": "Select Key Result"}, "actions": {"link": "<PERSON> Result"}, "notifications": {"linkSuccess": "Key result linked successfully", "linkError": "Error linking key result", "unlinkSuccess": "Key result unlinked successfully", "unlinkError": "Error unlinking key result", "selectKeyResult": "Please select a key result"}}, "statistics": {"title": "Statistics", "description": "View reports and task statistics", "personalPerformance": "Personal Performance", "projectPerformance": "Project Performance", "teamPerformance": "Team Performance", "filters": {"project": "Project", "selectProject": "Select project", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date"}, "metrics": {"totalTasks": "Total Tasks", "completedTasks": "Completed", "averageScore": "Average Score", "completionRate": "Completion Rate"}, "team": {"member": "Member", "completedTasks": "Completed Tasks", "totalTasks": "Total Tasks", "completionRate": "Completion Rate", "averageScore": "Average Score"}, "noData": "No data available"}, "dashboard": {"title": "Dashboard", "description": "Overview of your tasks and projects", "filters": {"selectProject": "All Projects", "allProjects": "All Projects", "startDate": "Start Date", "endDate": "End Date"}, "viewDetailedStats": "View Detailed Statistics", "stats": {"totalTasks": "Total Tasks", "pendingTasks": "Pending Tasks", "completionRate": "Completion Rate", "projects": "Projects", "inSystem": "in system", "needAttention": "need attention", "efficiency": "efficiency", "active": "active"}, "charts": {"taskDistribution": "Task Distribution", "byStatus": "By Status", "byPriority": "By Priority", "completionRate": "Completion Rate", "completed": "Completed", "remaining": "Remaining", "tasks": "tasks", "count": "Count", "noData": "No data available"}, "recentTasks": "Recent Tasks", "projects": "Projects", "viewAll": "View All", "noTasks": "No tasks found", "noProjects": "No projects found"}}