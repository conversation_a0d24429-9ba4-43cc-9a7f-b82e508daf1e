import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';

import {
  <PERSON>ton,
  Card,
  Chip,
  Divider,
  IconCard,
  Tabs,
  Typography,
} from '@/shared/components/common';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { formatDate } from '@/shared/utils/date';

import SlideInForm from '@/shared/components/common/SlideInForm';
import AttachmentList from '../components/attachments/AttachmentList';
import CommentList from '../components/comments/CommentList';
import LinkKeyResultForm from '../components/okr/LinkKeyResultForm';
import TaskKeyResultList from '../components/okr/TaskKeyResultList';
import SubtaskList from '../components/SubtaskList';
import TaskForm from '../components/TaskForm';
import { useAttachments } from '../hooks/useAttachments';
import { useComments } from '../hooks/useComments';
import { useSubtasks, useTask } from '../hooks/useTasks';
import { TaskPriority, TaskStatus } from '../types/task.types';

/**
 * Task detail page
 */
const TaskDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const taskId = parseInt(id || '0', 10);
  const { t } = useTranslation(['common', 'todolist']);
  const navigate = useNavigate();

  const {
    isVisible: isSubtaskFormOpen,
    showForm: showSubtaskForm,
    hideForm: hideSubtaskForm,
  } = useSlideForm();
  const {
    isVisible: isKeyResultFormOpen,
    showForm: showKeyResultForm,
    hideForm: hideKeyResultForm,
  } = useSlideForm();
  const [activeTab, setActiveTab] = useState<string>('details');

  // Fetch task details
  const { data: task, isLoading, refetch } = useTask(taskId);

  // Fetch subtasks
  const { data: subtasksData, isLoading: isLoadingSubtasks } = useSubtasks(taskId);

  // Fetch comments
  const { data: commentsData, isLoading: isLoadingComments } = useComments(taskId);

  // Fetch attachments
  const { data: attachmentsData, isLoading: isLoadingAttachments } = useAttachments(taskId);

  // Handle subtask form submit
  const handleSubtaskSubmit = () => {
    hideSubtaskForm();
    refetch();
  };

  // Handle key result form submit
  const handleKeyResultSubmit = () => {
    hideKeyResultForm();
    refetch();
  };

  // Handle back button click
  const handleBack = () => {
    navigate('/todolist/tasks');
  };

  // Get status badge color
  const getStatusBadgeColor = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-gray-100 text-gray-800';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case TaskStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return 'bg-green-100 text-green-800';
      case TaskPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800';
      case TaskPriority.HIGH:
        return 'bg-orange-100 text-orange-800';
      case TaskPriority.URGENT:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority text
  const getPriorityText = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return t('todolist:task.priority.low', 'Low');
      case TaskPriority.MEDIUM:
        return t('todolist:task.priority.medium', 'Medium');
      case TaskPriority.HIGH:
        return t('todolist:task.priority.high', 'High');
      case TaskPriority.URGENT:
        return t('todolist:task.priority.urgent', 'Urgent');
      default:
        return '';
    }
  };

  // Get status text
  const getStatusText = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return t('todolist:task.status.todo', 'To Do');
      case TaskStatus.IN_PROGRESS:
        return t('todolist:task.status.inProgress', 'In Progress');
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return t('todolist:task.status.done', 'Done');
      case TaskStatus.REJECTED:
        return t('todolist:task.status.cancelled', 'Cancelled');
      default:
        return '';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg text-gray-500">{t('common:loading', 'Loading...')}</div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="text-center py-8">
        <Typography variant="h4" className="mb-2">
          {t('todolist:task.notFound', 'Task not found')}
        </Typography>
        <Button onClick={handleBack} className="mt-4">
          {t('common:back', 'Back')}
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h4">{task.title}</Typography>
        <div className="flex space-x-2">
          <IconCard icon="arrow-left" onClick={handleBack} title={t('common:back', 'Back')} />
        </div>
      </div>

      {/* Add Subtask Form */}
      <SlideInForm isVisible={isSubtaskFormOpen}>
        <TaskForm parentId={taskId} onSubmit={handleSubtaskSubmit} onCancel={hideSubtaskForm} />
      </SlideInForm>

      {/* Link Key Result Form */}
      <SlideInForm isVisible={isKeyResultFormOpen}>
        <LinkKeyResultForm
          taskId={taskId}
          onSubmit={handleKeyResultSubmit}
          onCancel={hideKeyResultForm}
        />
      </SlideInForm>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="lg:col-span-2">
          <Card>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={[
                {
                  key: 'details',
                  label: t('todolist:task.tabs.details', 'Details'),
                  children: null,
                },
                {
                  key: 'subtasks',
                  label: t('todolist:task.tabs.subtasks', 'Subtasks'),
                  children: null,
                },
                {
                  key: 'comments',
                  label: t('todolist:task.tabs.comments', 'Comments'),
                  children: null,
                },
                {
                  key: 'attachments',
                  label: t('todolist:task.tabs.attachments', 'Attachments'),
                  children: null,
                },
                {
                  key: 'keyResults',
                  label: t('todolist:task.tabs.keyResults', 'Key Results'),
                  children: null,
                },
              ]}
            />

            <div className="p-4">
              {activeTab === 'details' && (
                <div className="space-y-4">
                  <div>
                    <Typography variant="h6" className="mb-2">
                      {t('todolist:task.fields.description', 'Description')}
                    </Typography>
                    <Typography variant="body1">
                      {task.description ||
                        t('todolist:task.noDescription', 'No description provided')}
                    </Typography>
                  </div>

                  <Divider />

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Typography variant="subtitle2" className="text-gray-500">
                        {t('todolist:task.fields.createdAt', 'Created At')}
                      </Typography>
                      <Typography variant="body2">{formatDate(task.createdAt || 0)}</Typography>
                    </div>
                    <div>
                      <Typography variant="subtitle2" className="text-gray-500">
                        {t('todolist:task.fields.updatedAt', 'Updated At')}
                      </Typography>
                      <Typography variant="body2">{formatDate(task.updatedAt || 0)}</Typography>
                    </div>
                    {task.completedAt && (
                      <div>
                        <Typography variant="subtitle2" className="text-gray-500">
                          {t('todolist:task.fields.completedAt', 'Completed At')}
                        </Typography>
                        <Typography variant="body2">{formatDate(task.completedAt)}</Typography>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {activeTab === 'subtasks' && (
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <Typography variant="h6">
                      {t('todolist:task.subtasks.title', 'Subtasks')}
                    </Typography>
                    <Button size="sm" onClick={showSubtaskForm}>
                      {t('todolist:task.subtasks.add', 'Add Subtask')}
                    </Button>
                  </div>

                  <SubtaskList
                    subtasks={subtasksData?.items || []}
                    isLoading={isLoadingSubtasks}
                    onRefresh={refetch}
                  />
                </div>
              )}

              {activeTab === 'comments' && (
                <CommentList
                  taskId={taskId}
                  comments={commentsData?.items || []}
                  isLoading={isLoadingComments}
                  onRefresh={refetch}
                />
              )}

              {activeTab === 'attachments' && (
                <AttachmentList
                  taskId={taskId}
                  attachments={attachmentsData?.items || []}
                  isLoading={isLoadingAttachments}
                  onRefresh={refetch}
                />
              )}

              {activeTab === 'keyResults' && (
                <TaskKeyResultList taskId={taskId} onAddKeyResult={showKeyResultForm} />
              )}
            </div>
          </Card>
        </div>

        <div>
          <Card>
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:task.details.title', 'Task Details')}
              </Typography>

              <div className="space-y-4">
                <div>
                  <Typography variant="subtitle2" className="text-gray-500 mb-1">
                    {t('todolist:task.fields.status', 'Status')}
                  </Typography>
                  <div className="flex items-center space-x-2">
                    <Chip className={getStatusBadgeColor(task.status)}>
                      {getStatusText(task.status)}
                    </Chip>
                  </div>
                </div>

                <div>
                  <Typography variant="subtitle2" className="text-gray-500 mb-1">
                    {t('todolist:task.fields.priority', 'Priority')}
                  </Typography>
                  <Chip className={getPriorityBadgeColor(task.priority)}>
                    {getPriorityText(task.priority)}
                  </Chip>
                </div>

                <div>
                  <Typography variant="subtitle2" className="text-gray-500 mb-1">
                    {t('todolist:task.fields.assignee', 'Assignee')}
                  </Typography>
                  <Typography variant="body2">
                    {task.assigneeId
                      ? `${t('todolist:task.userIdLabel', 'User ID')}: ${task.assigneeId}`
                      : t('todolist:task.noAssignee', 'No assignee')}
                  </Typography>
                </div>

                {task.expectedStars && (
                  <div>
                    <Typography variant="subtitle2" className="text-gray-500 mb-1">
                      {t('todolist:task.fields.expectedStars', 'Expected Stars')}
                    </Typography>
                    <div className="flex">
                      {Array.from({ length: task.expectedStars }).map((_, index) => (
                        <span key={index} className="text-yellow-400">
                          ★
                        </span>
                      ))}
                      {Array.from({ length: 5 - (task.expectedStars || 0) }).map((_, index) => (
                        <span key={index} className="text-gray-300">
                          ★
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {task.awardedStars && (
                  <div>
                    <Typography variant="subtitle2" className="text-gray-500 mb-1">
                      {t('todolist:task.fields.awardedStars', 'Awarded Stars')}
                    </Typography>
                    <div className="flex">
                      {Array.from({ length: task.awardedStars }).map((_, index) => (
                        <span key={index} className="text-yellow-400">
                          ★
                        </span>
                      ))}
                      {Array.from({ length: 5 - (task.awardedStars || 0) }).map((_, index) => (
                        <span key={index} className="text-gray-300">
                          ★
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TaskDetailPage;
