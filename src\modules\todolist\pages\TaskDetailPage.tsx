import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';

import { <PERSON><PERSON>, Card, Tabs, Typography } from '@/shared/components/common';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';

import SlideInForm from '@/shared/components/common/SlideInForm';
import AttachmentList from '../components/attachments/AttachmentList';
import LinkKeyResultForm from '../components/okr/LinkKeyResultForm';
import TaskKeyResultList from '../components/okr/TaskKeyResultList';
import {
  TaskChatSection,
  TaskDetailHeader,
  TaskDetailInfo,
  TaskDetailSidebar,
} from '../components/task-detail';
import TaskForm from '../components/TaskForm';
import { useAttachments } from '../hooks/useAttachments';
import { useComments, useCreateComment } from '../hooks/useComments';
import { useSubtasks, useTask, useUpdateTaskStatus } from '../hooks/useTasks';
import { TaskPriority, TaskStatus } from '../types/task.types';

/**
 * Task detail page
 */
const TaskDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const taskId = parseInt(id || '0', 10);
  const { t } = useTranslation(['common', 'todolist']);
  const navigate = useNavigate();

  const {
    isVisible: isSubtaskFormOpen,
    showForm: showSubtaskForm,
    hideForm: hideSubtaskForm,
  } = useSlideForm();
  const {
    isVisible: isKeyResultFormOpen,
    showForm: showKeyResultForm,
    hideForm: hideKeyResultForm,
  } = useSlideForm();
  const [activeTab, setActiveTab] = useState<string>('details');

  // Fetch task details
  const { data: task, isLoading, refetch } = useTask(taskId);

  // Fetch subtasks
  const { data: subtasksData, isLoading: isLoadingSubtasks } = useSubtasks(taskId);

  // Fetch comments
  const { data: commentsData, isLoading: isLoadingComments } = useComments(taskId);

  // Fetch attachments
  const { data: attachmentsData, isLoading: isLoadingAttachments } = useAttachments(taskId);

  // Mutations
  const { mutateAsync: updateTaskStatus } = useUpdateTaskStatus();
  const { mutateAsync: createComment } = useCreateComment();

  // Handle subtask form submit
  const handleSubtaskSubmit = () => {
    hideSubtaskForm();
    refetch();
  };

  // Handle key result form submit
  const handleKeyResultSubmit = () => {
    hideKeyResultForm();
    refetch();
  };

  // Handle back button click
  const handleBack = () => {
    navigate('/todolist/tasks');
  };

  // Handle reload
  const handleReload = () => {
    refetch();
  };

  // Handle mark complete
  const handleMarkComplete = async () => {
    if (!task) return;

    try {
      const newStatus =
        task.status === TaskStatus.COMPLETED ? TaskStatus.PENDING : TaskStatus.COMPLETED;

      await updateTaskStatus({ taskId, status: newStatus });
      NotificationUtil.success(
        t('todolist:task.notifications.statusUpdateSuccess', 'Task status updated successfully')
      );
      refetch();
    } catch (error) {
      console.error('Error updating task status:', error);
      NotificationUtil.error(
        t('todolist:task.notifications.statusUpdateError', 'Error updating task status')
      );
    }
  };

  // Handle status update
  const handleUpdateStatus = async (status: TaskStatus) => {
    try {
      await updateTaskStatus({ taskId, status });
      NotificationUtil.success(
        t('todolist:task.notifications.statusUpdateSuccess', 'Task status updated successfully')
      );
      refetch();
    } catch (error) {
      console.error('Error updating task status:', error);
      NotificationUtil.error(
        t('todolist:task.notifications.statusUpdateError', 'Error updating task status')
      );
    }
  };

  // Handle priority update
  const handleUpdatePriority = async (priority: TaskPriority) => {
    // TODO: Implement priority update API
    console.log('Update priority:', priority);
    NotificationUtil.success('Priority updated successfully');
  };

  // Handle assignee update
  const handleUpdateAssignee = async (assigneeId: string) => {
    // TODO: Implement assignee update API
    console.log('Update assignee:', assigneeId);
    NotificationUtil.success('Assignee updated successfully');
  };

  // Handle expected stars update
  const handleUpdateExpectedStars = async (stars: number) => {
    // TODO: Implement expected stars update API
    console.log('Update expected stars:', stars);
    NotificationUtil.success('Expected stars updated successfully');
  };

  // Handle add comment
  const handleAddComment = async (content: string) => {
    try {
      await createComment({ taskId, content });
      NotificationUtil.success(
        t('todolist:task.comments.notifications.createSuccess', 'Comment added successfully')
      );
      refetch();
    } catch (error) {
      console.error('Error adding comment:', error);
      NotificationUtil.error(
        t('todolist:task.comments.notifications.createError', 'Error adding comment')
      );
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg text-gray-500">{t('common:loading', 'Loading...')}</div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="text-center py-8">
        <Typography variant="h4" className="mb-2">
          {t('todolist:task.notFound', 'Task not found')}
        </Typography>
        <Button onClick={handleBack} className="mt-4">
          {t('common:back', 'Back')}
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <TaskDetailHeader
        title={task.title}
        onBack={handleBack}
        onReload={() => refetch()}
        onMarkComplete={handleMarkComplete}
        isCompleted={task.status === TaskStatus.COMPLETED}
      />

      {/* Add Subtask Form */}
      <SlideInForm isVisible={isSubtaskFormOpen}>
        <TaskForm parentId={taskId} onSubmit={handleSubtaskSubmit} onCancel={hideSubtaskForm} />
      </SlideInForm>

      {/* Link Key Result Form */}
      <SlideInForm isVisible={isKeyResultFormOpen}>
        <LinkKeyResultForm
          taskId={taskId}
          onSubmit={handleKeyResultSubmit}
          onCancel={hideKeyResultForm}
        />
      </SlideInForm>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="lg:col-span-2">
          <Card>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={[
                {
                  key: 'details',
                  label: t('todolist:task.tabs.details', 'Details'),
                  children: null,
                },
                {
                  key: 'attachments',
                  label: t('todolist:task.tabs.attachments', 'Attachments'),
                  children: null,
                },
                {
                  key: 'keyResults',
                  label: t('todolist:task.tabs.keyResults', 'Key Results'),
                  children: null,
                },
              ]}
            />

            <div className="p-4">
              {activeTab === 'details' && <TaskDetailInfo task={task} />}

              {activeTab === 'attachments' && (
                <AttachmentList
                  taskId={taskId}
                  attachments={attachmentsData?.items || []}
                  isLoading={isLoadingAttachments}
                  onRefresh={refetch}
                />
              )}

              {activeTab === 'keyResults' && (
                <TaskKeyResultList taskId={taskId} onAddKeyResult={showKeyResultForm} />
              )}
            </div>
          </Card>
        </div>

        <div>
          <TaskDetailSidebar
            task={task}
            onUpdateStatus={handleUpdateStatus}
            onUpdatePriority={handleUpdatePriority}
            onUpdateAssignee={handleUpdateAssignee}
            onUpdateExpectedStars={handleUpdateExpectedStars}
          />
        </div>
      </div>

      {/* Chat Section for Comments and Subtasks */}
      <TaskChatSection
        taskId={taskId}
        comments={commentsData?.items || []}
        subtasks={subtasksData?.items || []}
        isLoadingComments={isLoadingComments}
        isLoadingSubtasks={isLoadingSubtasks}
        onAddComment={handleAddComment}
        onAddSubtask={showSubtaskForm}
        onRefresh={refetch}
      />
    </div>
  );
};

export default TaskDetailPage;
